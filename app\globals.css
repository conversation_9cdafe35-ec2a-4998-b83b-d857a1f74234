@tailwind base;
@tailwind components;
@tailwind utilities;

/* Solana Wallet Adapter Styles */
@import '../styles/wallet-adapter.css';

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;

  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;

  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;

  --primary: 244 75% 60%;
  --primary-foreground: 210 40% 98%;

  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;

  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;

  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;

  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;

  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 244 75% 60%;

  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;

  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;

  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;

  --primary: 244 75% 60%;
  --primary-foreground: 222.2 47.4% 11.2%;

  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;

  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;

  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;

  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;

  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 244 75% 60%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Mobile-first responsive utilities */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .mobile-text {
    @apply text-sm sm:text-base;
  }

  .mobile-heading {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .mobile-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  /* Prevent horizontal scrolling */
  .no-scroll-x {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Mobile-friendly button sizes */
  .mobile-btn {
    @apply px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base;
  }
}

/* Fix white hover color issues */
@layer components {
  /* Fix problematic white background hovers that make text invisible */
  .hover\:bg-white:hover {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  /* Fix table row hover states for better visibility */
  tr:hover {
    @apply bg-gray-50 dark:bg-gray-800;
  }

  /* Override any white background hovers that cause visibility issues */
  [class*="hover:bg-white"]:hover {
    @apply bg-gray-100 dark:bg-gray-800 !important;
  }

  /* Mobile-specific improvements */
  @media (max-width: 768px) {
    /* Ensure proper touch targets */
    button, a, [role="button"] {
      min-height: 44px;
      min-width: 44px;
    }

    /* Better spacing for mobile */
    .container {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    /* Prevent text from being too small on mobile */
    body {
      font-size: 16px;
      line-height: 1.5;
    }

    /* Ensure dropdowns and modals work well on mobile */
    .dropdown-content, .modal-content {
      max-width: calc(100vw - 2rem);
      max-height: calc(100vh - 4rem);
    }
  }
}
